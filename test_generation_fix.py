#!/usr/bin/env python3
"""
Test script to verify the generation fixes for test_intervention function.
"""

import torch
import logging
from itas.core.config import ModelConfig, SAEConfig
from itas.core.model_loader import UniversalModelLoader
from itas.core.sae import SAE
from itas.analysis.representation_engineer import RepresentationEngineer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_with_generation_model():
    """Test with a model loaded for generation."""
    print("🧪 Testing with model loaded for generation...")
    
    # Load model WITH generation capability
    model_config = ModelConfig(
        model_name="microsoft/DialoGPT-small",
        use_flash_attention=False,
        torch_dtype="float32",
        device_map=None,
        trust_remote_code=False,
        load_for_generation=True,  # This is the key!
    )
    
    try:
        # Load model
        print("📥 Loading model with generation capability...")
        model_loader = UniversalModelLoader(model_config)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        
        print(f"✅ Model loaded! Has generate method: {hasattr(model, 'generate')}")
        print(f"   Model type: {type(model)}")
        
        # Create a simple SAE for testing
        print("🔧 Creating test SAE...")
        hidden_size = model.config.hidden_size
        sae = SAE(
            input_size=hidden_size,
            hidden_size=hidden_size * 4,
            activation_fn="relu",
            normalize_decoder=True,
            device=model.device,
            dtype=torch.float32,
        )
        
        # Create representation engineer
        print("🎛️ Setting up representation engineer...")
        engineer = RepresentationEngineer(
            model=model,
            tokenizer=tokenizer,
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
        )
        
        # Create a simple intervention function
        def test_intervention_fn(module, input, output):
            """Simple test intervention that adds small noise."""
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            # Add small random noise
            noise = torch.randn_like(hidden_states) * 0.01
            modified = hidden_states + noise
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        # Test prompts
        test_prompts = [
            "Hello, how are you?",
            "What is the weather like?",
        ]
        
        print("🧪 Testing intervention with text generation...")
        
        # Test intervention
        results = engineer.test_intervention(
            test_prompts,
            test_intervention_fn,
            generation_kwargs={
                "max_new_tokens": 15,
                "do_sample": False,
                "pad_token_id": tokenizer.eos_token_id,
            }
        )
        
        print("✅ Text generation test completed!")
        print(f"📊 Is activation comparison: {results.is_activation_comparison()}")
        print(f"📊 Number of results: {len(results)}")
        
        # Show the actual generated text
        print("\n📝 Generated Results:")
        for i, (prompt, result) in enumerate(zip(test_prompts, results)):
            print(f"\nPrompt {i+1}: {prompt}")
            if isinstance(result, dict):
                print(f"  Original: {result.get('original', 'N/A')}")
                print(f"  Modified: {result.get('modified', 'N/A')}")
            else:
                print(f"  Unexpected result type: {type(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_force_generation():
    """Test with force_generation=True on a non-generation model."""
    print("\n🧪 Testing with force_generation=True...")
    
    # Load model WITHOUT generation capability
    model_config = ModelConfig(
        model_name="microsoft/DialoGPT-small",
        use_flash_attention=False,
        torch_dtype="float32",
        device_map=None,
        trust_remote_code=False,
        load_for_generation=False,  # No generation capability
    )
    
    try:
        # Load model
        print("📥 Loading model without generation capability...")
        model_loader = UniversalModelLoader(model_config)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        
        print(f"✅ Model loaded! Has generate method: {hasattr(model, 'generate')}")
        print(f"   Model type: {type(model)}")
        
        # Create a simple SAE for testing
        print("🔧 Creating test SAE...")
        hidden_size = model.config.hidden_size
        sae = SAE(
            input_size=hidden_size,
            hidden_size=hidden_size * 4,
            activation_fn="relu",
            normalize_decoder=True,
            device=model.device,
            dtype=torch.float32,
        )
        
        # Create representation engineer
        print("🎛️ Setting up representation engineer...")
        engineer = RepresentationEngineer(
            model=model,
            tokenizer=tokenizer,
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
        )
        
        # Simple intervention function
        def test_intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            # Add small noise
            noise = torch.randn_like(hidden_states) * 0.01
            modified = hidden_states + noise
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        # Test prompts
        test_prompts = ["Hello, how are you?"]
        
        print("🧪 Testing intervention with force_generation=True...")
        
        # Test intervention with force_generation
        results = engineer.test_intervention(
            test_prompts,
            test_intervention_fn,
            generation_kwargs={
                "max_new_tokens": 10,
                "do_sample": False,
                "pad_token_id": tokenizer.eos_token_id,
            },
            force_generation=True,  # Try to add generation capability
        )
        
        print("✅ Force generation test completed!")
        print(f"📊 Is activation comparison: {results.is_activation_comparison()}")
        print(f"📊 Model now has generate method: {hasattr(model, 'generate')}")
        
        # Show results
        for i, result in enumerate(results):
            print(f"\nResult {i+1}: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Force generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_method():
    """Test the new from_model_name convenience method."""
    print("\n🧪 Testing RepresentationEngineer.from_model_name()...")
    
    try:
        # Create a simple SAE for testing
        print("🔧 Creating test SAE...")
        hidden_size = 768  # DialoGPT-small hidden size
        sae = SAE(
            input_size=hidden_size,
            hidden_size=hidden_size * 4,
            activation_fn="relu",
            normalize_decoder=True,
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype=torch.float32,
        )
        
        print("🎛️ Creating RepresentationEngineer with from_model_name...")
        engineer = RepresentationEngineer.from_model_name(
            model_name="microsoft/DialoGPT-small",
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
            load_for_generation=True,  # Enable generation
            use_flash_attention=False,
            torch_dtype="float32",
        )
        
        print(f"✅ Engineer created! Model has generate method: {hasattr(engineer.model, 'generate')}")
        
        # Simple test
        def test_intervention_fn(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            noise = torch.randn_like(hidden_states) * 0.01
            modified = hidden_states + noise
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        test_prompts = ["Hello!"]
        
        results = engineer.test_intervention(
            test_prompts,
            test_intervention_fn,
            generation_kwargs={
                "max_new_tokens": 5,
                "do_sample": False,
                "pad_token_id": engineer.tokenizer.eos_token_id,
            }
        )
        
        print("✅ Convenience method test completed!")
        print(f"📊 Is activation comparison: {results.is_activation_comparison()}")
        
        for result in results:
            print(f"Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Convenience method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting generation capability tests...\n")
    
    success1 = test_with_generation_model()
    success2 = test_with_force_generation()
    success3 = test_convenience_method()
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! The test_intervention function now properly supports text generation.")
        print("\n📋 Summary:")
        print("✅ Models loaded with load_for_generation=True support text generation")
        print("✅ force_generation=True can add generation capability to existing models")
        print("✅ RepresentationEngineer.from_model_name() provides convenient model loading")
        print("✅ Proper fallback to activation comparison when generation is not available")
    else:
        print("\n💥 Some tests failed. Please check the error messages above.")
