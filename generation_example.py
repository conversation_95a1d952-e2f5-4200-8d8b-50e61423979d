#!/usr/bin/env python3
"""
Example showing the correct way to use test_intervention for text generation.
"""

import torch
from itas.core.sae import SAE
from itas.analysis.representation_engineer import RepresentationEngineer

def main():
    print("🎯 Example: Proper text generation with test_intervention")
    print("=" * 60)
    
    # Method 1: Using the convenience method (RECOMMENDED)
    print("\n1️⃣ Method 1: Using RepresentationEngineer.from_model_name() (RECOMMENDED)")
    print("-" * 50)
    
    try:
        # Create SAE
        hidden_size = 768  # DialoGPT-small hidden size
        sae = SAE(
            input_size=hidden_size,
            hidden_size=hidden_size * 4,
            activation_fn="relu",
            normalize_decoder=True,
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype=torch.float32,
        )
        
        # Create engineer with generation capability
        engineer = RepresentationEngineer.from_model_name(
            model_name="microsoft/DialoGPT-small",
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
            load_for_generation=True,  # This enables text generation!
            use_flash_attention=False,
            torch_dtype="float32",
        )
        
        print(f"✅ Model has generation capability: {hasattr(engineer.model, 'generate')}")
        
        # Create intervention function
        def add_positivity_intervention(module, input, output):
            """Add a small positive bias to encourage more positive responses."""
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            # Add small positive bias
            positive_bias = torch.ones_like(hidden_states) * 0.02
            modified = hidden_states + positive_bias
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        # Test prompts
        test_prompts = [
            "How are you feeling today?",
            "What do you think about the weather?",
        ]
        
        print("\n🧪 Testing intervention with text generation...")
        
        # Run intervention test
        results = engineer.test_intervention(
            test_prompts,
            add_positivity_intervention,
            generation_kwargs={
                "max_new_tokens": 20,
                "temperature": 0.7,
                "do_sample": True,
                "pad_token_id": engineer.tokenizer.eos_token_id,
            }
        )
        
        print("✅ Text generation completed!")
        print(f"Is activation comparison: {results.is_activation_comparison()}")
        
        # Display results
        print("\n📝 Generated Text Results:")
        for i, (prompt, result) in enumerate(zip(test_prompts, results)):
            print(f"\nPrompt {i+1}: {prompt}")
            print(f"  Original: {result['original']}")
            print(f"  Modified: {result['modified']}")
        
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    # Method 2: Manual model loading
    print("\n\n2️⃣ Method 2: Manual model loading")
    print("-" * 50)
    
    try:
        from itas.utils import load_model_and_tokenizer
        
        # Load model with generation capability
        model, tokenizer = load_model_and_tokenizer(
            model_name="microsoft/DialoGPT-small",
            load_for_generation=True,  # Key parameter!
            use_flash_attention=False,
            torch_dtype="float32",
        )
        
        print(f"✅ Model loaded with generation: {hasattr(model, 'generate')}")
        
        # Create engineer
        engineer = RepresentationEngineer(
            model=model,
            tokenizer=tokenizer,
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
        )
        
        # Simple intervention
        def simple_intervention(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            # Add tiny noise
            noise = torch.randn_like(hidden_states) * 0.005
            modified = hidden_states + noise
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        # Test
        results = engineer.test_intervention(
            ["Hello there!"],
            simple_intervention,
            generation_kwargs={
                "max_new_tokens": 15,
                "do_sample": False,
                "pad_token_id": tokenizer.eos_token_id,
            }
        )
        
        print("✅ Manual loading method works!")
        for result in results:
            print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    # Method 3: Using force_generation for existing models
    print("\n\n3️⃣ Method 3: Using force_generation for existing models")
    print("-" * 50)
    
    try:
        from itas.utils import load_model_and_tokenizer
        
        # Load model WITHOUT generation capability
        model, tokenizer = load_model_and_tokenizer(
            model_name="microsoft/DialoGPT-small",
            load_for_generation=False,  # No generation initially
            use_flash_attention=False,
            torch_dtype="float32",
        )
        
        print(f"Model initially has generation: {hasattr(model, 'generate')}")
        
        # Create engineer
        engineer = RepresentationEngineer(
            model=model,
            tokenizer=tokenizer,
            sae=sae,
            hook_layer=1,
            hook_name="transformer.h.1.mlp",
        )
        
        # Simple intervention
        def simple_intervention(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
                other_outputs = output[1:]
            else:
                hidden_states = output
                other_outputs = ()
            
            noise = torch.randn_like(hidden_states) * 0.005
            modified = hidden_states + noise
            
            if other_outputs:
                return (modified,) + other_outputs
            else:
                return modified
        
        # Test with force_generation=True
        results = engineer.test_intervention(
            ["Hi!"],
            simple_intervention,
            generation_kwargs={
                "max_new_tokens": 10,
                "do_sample": False,
                "pad_token_id": tokenizer.eos_token_id,
            },
            force_generation=True,  # Try to add generation capability
        )
        
        print(f"✅ After force_generation, model has generate: {hasattr(model, 'generate')}")
        print(f"Is activation comparison: {results.is_activation_comparison()}")
        
        for result in results:
            print(f"Result: {result}")
        
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
    
    print("\n\n🎉 Example complete!")
    print("\n📋 Key takeaways:")
    print("✅ Use load_for_generation=True to enable text generation")
    print("✅ RepresentationEngineer.from_model_name() is the easiest method")
    print("✅ force_generation=True can add generation to existing models")
    print("✅ Without generation, the function falls back to activation comparison")

if __name__ == "__main__":
    main()
